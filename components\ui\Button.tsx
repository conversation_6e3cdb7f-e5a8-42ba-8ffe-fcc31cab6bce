
import React from 'react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  children,
  leftIcon,
  rightIcon,
  className,
  ...props
}) => {
  const baseStyle = "relative inline-flex items-center justify-center font-semibold rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-opacity-75 transition-all duration-300 ease-in-out transform hover:scale-105 group overflow-hidden";

  const variantStyles = {
    primary: 'bg-coralOrange hover:bg-sunsetOrange text-white focus:ring-coralOrange shadow-warm-glow hover:shadow-storybook animate-gentle-glow border-2 border-coralOrange/30',
    secondary: 'bg-butteryYellow hover:bg-sunsetOrange text-billieBlack focus:ring-butteryYellow shadow-glow-yellow hover:shadow-storybook animate-warm-pulse border-2 border-butteryYellow/30',
    outline: 'bg-transparent hover:bg-pastelPurple/20 text-billieAccent border-2 border-pastelPurple hover:text-billieHeading focus:ring-pastelPurple hover:border-coralOrange hover:shadow-warm-glow animate-storybook-wiggle',
  };

  const sizeStyles = {
    sm: 'px-3 py-1.5 text-xs',
    md: 'px-5 py-2.5 text-sm',
    lg: 'px-7 py-3 text-base',
  };

  return (
    <button
      className={`${baseStyle} ${variantStyles[variant]} ${sizeStyles[size]} ${className || ''}`}
      {...props}
    >
      {/* Paper texture background */}
      <div className="absolute inset-0 bg-paper-texture opacity-30 group-hover:opacity-50 transition-opacity duration-300" />

      {/* Warm shimmer effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-butteryYellow to-transparent opacity-0 group-hover:opacity-20 transition-opacity duration-500 transform -translate-x-full group-hover:translate-x-full group-hover:transition-transform group-hover:duration-1000" />

      {/* Content */}
      <div className="relative z-10 flex items-center">
        {leftIcon && <span className="mr-2">{leftIcon}</span>}
        <span className="font-storybook">{children}</span>
        {rightIcon && <span className="ml-2">{rightIcon}</span>}
      </div>
    </button>
  );
};

export default Button;
    