
import React from 'react';
import { Routes, Route, useLocation } from 'react-router-dom';
import Navbar from './components/layout/Navbar';
import Footer from './components/layout/Footer';
import Web3Background from './components/ui/Web3Background';
import HomePage from './pages/HomePage';
import StoryPage from './pages/StoryPage';
import NftPage from './pages/NftPage';
import EducationPage from './pages/EducationPage'; // Ensured relative path
import CommunityPage from './pages/CommunityPage'; // Ensured relative path

const App: React.FC = () => {
  const location = useLocation();

  React.useEffect(() => {
    window.scrollTo(0, 0);
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex flex-col bg-sky-gradient relative overflow-hidden">
      {/* Storybook Background Effects */}
      <Web3Background />

      {/* Paper texture overlay */}
      <div
        className="fixed inset-0 opacity-20 pointer-events-none z-0"
        style={{
          backgroundImage: 'radial-gradient(circle at 20% 50%, rgba(247, 231, 206, 0.6) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(245, 245, 220, 0.4) 0%, transparent 50%)',
          backgroundSize: '100px 100px',
        }}
      />

      {/* Main content */}
      <div className="relative z-10 flex flex-col min-h-screen">
        <Navbar />
        <main className="flex-grow">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/story" element={<StoryPage />} />
            <Route path="/nfts" element={<NftPage />} />
            <Route path="/education" element={<EducationPage />} />
            <Route path="/community" element={<CommunityPage />} />
          </Routes>
        </main>
        <Footer />
      </div>
    </div>
  );
};

export default App;
