import React from 'react';

interface NeonButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'coral' | 'yellow' | 'purple' | 'blue' | 'green';
  size?: 'sm' | 'md' | 'lg';
  glowIntensity?: 'low' | 'medium' | 'high';
  children: React.ReactNode;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const NeonButton: React.FC<NeonButtonProps> = ({
  variant = 'coral',
  size = 'md',
  glowIntensity = 'medium',
  children,
  leftIcon,
  rightIcon,
  className = '',
  ...props
}) => {
  const baseClasses = "relative inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-opacity-50 overflow-hidden group";

  const variantClasses = {
    coral: {
      bg: 'bg-coralOrange/20 hover:bg-coralOrange/30',
      text: 'text-coralOrange',
      border: 'border-2 border-coralOrange',
      glow: 'shadow-glow-coral hover:shadow-storybook',
      focus: 'focus:ring-coralOrange',
    },
    yellow: {
      bg: 'bg-butteryYellow/20 hover:bg-butteryYellow/30',
      text: 'text-butteryYellow',
      border: 'border-2 border-butteryYellow',
      glow: 'shadow-glow-yellow hover:shadow-storybook',
      focus: 'focus:ring-butteryYellow',
    },
    purple: {
      bg: 'bg-pastelPurple/20 hover:bg-pastelPurple/30',
      text: 'text-pastelPurple',
      border: 'border-2 border-pastelPurple',
      glow: 'shadow-glow-purple hover:shadow-storybook',
      focus: 'focus:ring-pastelPurple',
    },
    blue: {
      bg: 'bg-skyBlue/20 hover:bg-skyBlue/30',
      text: 'text-skyBlue',
      border: 'border-2 border-skyBlue',
      glow: 'shadow-soft-shadow hover:shadow-storybook',
      focus: 'focus:ring-skyBlue',
    },
    green: {
      bg: 'bg-mutedGreen/20 hover:bg-mutedGreen/30',
      text: 'text-mutedGreen',
      border: 'border-2 border-mutedGreen',
      glow: 'shadow-soft-shadow hover:shadow-storybook',
      focus: 'focus:ring-mutedGreen',
    },
  };

  const sizeClasses = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg',
  };

  const glowClasses = {
    low: 'animate-gentle-glow',
    medium: 'animate-warm-pulse',
    high: 'animate-gentle-glow animate-warm-pulse',
  };

  const currentVariant = variantClasses[variant];

  return (
    <button
      className={`
        ${baseClasses}
        ${currentVariant.bg}
        ${currentVariant.text}
        ${currentVariant.border}
        ${currentVariant.glow}
        ${currentVariant.focus}
        ${sizeClasses[size]}
        ${glowClasses[glowIntensity]}
        ${className}
      `}
      {...props}
    >
      {/* Animated background effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-current to-transparent opacity-0 group-hover:opacity-10 transition-opacity duration-300 transform -skew-x-12 group-hover:animate-pulse" />
      
      {/* Scanning line effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-20 transition-opacity duration-300 transform -translate-x-full group-hover:translate-x-full group-hover:transition-transform group-hover:duration-700" />
      
      {/* Content */}
      <div className="relative flex items-center space-x-2 z-10">
        {leftIcon && <span className="flex-shrink-0">{leftIcon}</span>}
        <span className="font-display font-bold tracking-wide">{children}</span>
        {rightIcon && <span className="flex-shrink-0">{rightIcon}</span>}
      </div>
      
      {/* Corner accents */}
      <div className="absolute top-0 left-0 w-2 h-2 border-t-2 border-l-2 border-current opacity-60" />
      <div className="absolute top-0 right-0 w-2 h-2 border-t-2 border-r-2 border-current opacity-60" />
      <div className="absolute bottom-0 left-0 w-2 h-2 border-b-2 border-l-2 border-current opacity-60" />
      <div className="absolute bottom-0 right-0 w-2 h-2 border-b-2 border-r-2 border-current opacity-60" />
    </button>
  );
};

export default NeonButton;
