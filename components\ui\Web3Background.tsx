import React, { useEffect, useRef } from 'react';

interface Particle {
  x: number;
  y: number;
  vx: number;
  vy: number;
  size: number;
  color: string;
  opacity: number;
}

const Web3Background: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const particlesRef = useRef<Particle[]>([]);
  const animationRef = useRef<number>();

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    const createParticles = () => {
      const particles: Particle[] = [];
      const colors = ['#FF7F50', '#F0E68C', '#DDA0DD', '#87CEEB', '#8FBC8F', '#F08080'];

      for (let i = 0; i < 30; i++) {
        particles.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          vx: (Math.random() - 0.5) * 0.3,
          vy: (Math.random() - 0.5) * 0.3,
          size: Math.random() * 4 + 2,
          color: colors[Math.floor(Math.random() * colors.length)],
          opacity: Math.random() * 0.4 + 0.3,
        });
      }

      particlesRef.current = particles;
    };

    const drawParticle = (particle: Particle) => {
      ctx.save();
      ctx.globalAlpha = particle.opacity;
      ctx.fillStyle = particle.color;
      ctx.shadowBlur = 8;
      ctx.shadowColor = particle.color;

      // Draw sparkle/star shape instead of circle
      const spikes = 5;
      const outerRadius = particle.size;
      const innerRadius = particle.size * 0.4;

      ctx.beginPath();
      for (let i = 0; i < spikes * 2; i++) {
        const radius = i % 2 === 0 ? outerRadius : innerRadius;
        const angle = (i * Math.PI) / spikes;
        const x = particle.x + Math.cos(angle) * radius;
        const y = particle.y + Math.sin(angle) * radius;
        if (i === 0) ctx.moveTo(x, y);
        else ctx.lineTo(x, y);
      }
      ctx.closePath();
      ctx.fill();
      ctx.restore();
    };

    const updateParticle = (particle: Particle) => {
      particle.x += particle.vx;
      particle.y += particle.vy;

      // Wrap around edges
      if (particle.x < 0) particle.x = canvas.width;
      if (particle.x > canvas.width) particle.x = 0;
      if (particle.y < 0) particle.y = canvas.height;
      if (particle.y > canvas.height) particle.y = 0;

      // Pulse opacity
      particle.opacity += Math.sin(Date.now() * 0.001 + particle.x * 0.01) * 0.01;
      particle.opacity = Math.max(0.1, Math.min(0.7, particle.opacity));
    };

    const drawConnections = () => {
      const particles = particlesRef.current;
      ctx.strokeStyle = 'rgba(139, 92, 246, 0.1)';
      ctx.lineWidth = 1;

      for (let i = 0; i < particles.length; i++) {
        for (let j = i + 1; j < particles.length; j++) {
          const dx = particles[i].x - particles[j].x;
          const dy = particles[i].y - particles[j].y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < 100) {
            ctx.globalAlpha = (100 - distance) / 100 * 0.3;
            ctx.beginPath();
            ctx.moveTo(particles[i].x, particles[i].y);
            ctx.lineTo(particles[j].x, particles[j].y);
            ctx.stroke();
          }
        }
      }
      ctx.globalAlpha = 1;
    };

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Draw soft cloud-like background patterns
      ctx.strokeStyle = 'rgba(255, 127, 80, 0.08)';
      ctx.lineWidth = 2;
      const cloudSize = 80;

      for (let x = 0; x < canvas.width; x += cloudSize) {
        for (let y = 0; y < canvas.height; y += cloudSize) {
          ctx.save();
          ctx.globalAlpha = 0.1;
          ctx.fillStyle = 'rgba(240, 230, 140, 0.3)';
          ctx.beginPath();
          ctx.arc(x + Math.sin(Date.now() * 0.001 + x) * 10, y + Math.cos(Date.now() * 0.001 + y) * 10, 15, 0, Math.PI * 2);
          ctx.fill();
          ctx.restore();
        }
      }

      // Update and draw particles
      particlesRef.current.forEach(particle => {
        updateParticle(particle);
        drawParticle(particle);
      });

      // Draw connections
      drawConnections();

      animationRef.current = requestAnimationFrame(animate);
    };

    resizeCanvas();
    createParticles();
    animate();

    window.addEventListener('resize', resizeCanvas);

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  return (
    <>
      <canvas
        ref={canvasRef}
        className="fixed inset-0 pointer-events-none z-0"
        style={{ background: 'transparent' }}
      />
      {/* Floating leaves effect */}
      <div className="fixed inset-0 pointer-events-none z-0 overflow-hidden">
        {Array.from({ length: 12 }).map((_, i) => (
          <div
            key={i}
            className="absolute text-2xl opacity-30 animate-leaf-fall"
            style={{
              left: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 15}s`,
              animationDuration: `${20 + Math.random() * 10}s`,
              color: ['#8FBC8F', '#F0E68C', '#FF7F50', '#DDA0DD'][Math.floor(Math.random() * 4)],
            }}
          >
            {['🍃', '🌸', '✨', '🦋', '🌿'][Math.floor(Math.random() * 5)]}
          </div>
        ))}
      </div>
      {/* Floating storybook elements */}
      <div className="fixed inset-0 pointer-events-none z-0 overflow-hidden">
        {Array.from({ length: 8 }).map((_, i) => (
          <div
            key={i}
            className="absolute text-lg font-storybook opacity-25 animate-paper-flutter whitespace-nowrap"
            style={{
              top: `${15 + i * 12}%`,
              right: `${Math.random() * 30}%`,
              animationDelay: `${i * 2}s`,
              color: '#A0522D',
            }}
          >
            {['Once upon a time...', '✨ Magic ✨', '🏰', '📚', '🌟', '🎭', '🎨', '🧸'][i]}
          </div>
        ))}
      </div>
    </>
  );
};

export default Web3Background;
