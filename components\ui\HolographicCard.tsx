import React, { useRef, useState } from 'react';

interface HolographicCardProps {
  children: React.ReactNode;
  className?: string;
  intensity?: 'low' | 'medium' | 'high';
  glowColor?: 'coral' | 'yellow' | 'purple' | 'blue' | 'green';
  onClick?: () => void;
}

const HolographicCard: React.FC<HolographicCardProps> = ({
  children,
  className = '',
  intensity = 'medium',
  glowColor = 'coral',
  onClick,
}) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!cardRef.current) return;

    const rect = cardRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    setMousePosition({ x, y });
  };

  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    setMousePosition({ x: 0, y: 0 });
  };

  const intensityClasses = {
    low: 'animate-gentle-glow',
    medium: 'animate-gentle-glow animate-warm-pulse',
    high: 'animate-gentle-glow animate-warm-pulse animate-paper-flutter',
  };

  const glowColorClasses = {
    coral: 'shadow-glow-coral border-coralOrange',
    yellow: 'shadow-glow-yellow border-butteryYellow',
    purple: 'shadow-glow-purple border-pastelPurple',
    blue: 'shadow-soft-shadow border-skyBlue',
    green: 'shadow-soft-shadow border-mutedGreen',
  };

  const gradientStyle = isHovered
    ? {
        background: `radial-gradient(circle at ${mousePosition.x}px ${mousePosition.y}px, rgba(255, 127, 80, 0.15) 0%, rgba(240, 230, 140, 0.1) 50%, transparent 70%)`,
      }
    : {};

  return (
    <div
      ref={cardRef}
      className={`
        relative overflow-hidden rounded-xl backdrop-blur-sm bg-parchment/80 border-2 transition-all duration-300 ease-in-out transform hover:scale-[1.02] cursor-pointer group
        ${intensityClasses[intensity]}
        ${glowColorClasses[glowColor]}
        ${isHovered ? 'shadow-storybook' : 'shadow-paper-shadow'}
        ${className}
      `}
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={onClick}
      role={onClick ? "button" : undefined}
      tabIndex={onClick ? 0 : undefined}
    >
      {/* Animated background gradient */}
      <div
        className="absolute inset-0 opacity-30 transition-opacity duration-300"
        style={gradientStyle}
      />

      {/* Scanning lines effect */}
      <div className="absolute inset-0 opacity-20">
        {Array.from({ length: 5 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-full h-px bg-gradient-to-r from-transparent via-current to-transparent animate-data-stream"
            style={{
              top: `${20 + i * 20}%`,
              animationDelay: `${i * 0.5}s`,
              animationDuration: '3s',
            }}
          />
        ))}
      </div>

      {/* Corner brackets */}
      <div className="absolute top-2 left-2 w-4 h-4 border-t-2 border-l-2 border-current opacity-60 group-hover:opacity-100 transition-opacity" />
      <div className="absolute top-2 right-2 w-4 h-4 border-t-2 border-r-2 border-current opacity-60 group-hover:opacity-100 transition-opacity" />
      <div className="absolute bottom-2 left-2 w-4 h-4 border-b-2 border-l-2 border-current opacity-60 group-hover:opacity-100 transition-opacity" />
      <div className="absolute bottom-2 right-2 w-4 h-4 border-b-2 border-r-2 border-current opacity-60 group-hover:opacity-100 transition-opacity" />

      {/* Paper texture overlay */}
      <div
        className="absolute inset-0 opacity-30 group-hover:opacity-50 transition-opacity"
        style={{
          backgroundImage: 'radial-gradient(circle at 30% 40%, rgba(255, 127, 80, 0.1) 0%, transparent 50%), radial-gradient(circle at 70% 80%, rgba(240, 230, 140, 0.1) 0%, transparent 50%)',
          backgroundSize: '40px 40px',
        }}
      />

      {/* Content */}
      <div className="relative z-10 p-6">
        {children}
      </div>

      {/* Warm shimmer effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-butteryYellow to-transparent opacity-0 group-hover:opacity-10 transition-opacity duration-700 transform -translate-x-full group-hover:translate-x-full group-hover:transition-transform group-hover:duration-1200" />
    </div>
  );
};

export default HolographicCard;
