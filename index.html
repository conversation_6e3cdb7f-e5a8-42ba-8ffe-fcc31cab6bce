
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" /> <!-- Replace with actual favicon -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Billie the Bear - Web3 Adventure</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              // Storybook Sky & Water - Bright tropical blues
              skyBlue: '#87CEEB',
              oceanBlue: '#4682B4',
              waterBlue: '#5DADE2',
              deepBlue: '#2E86AB',

              // Land & Terrain - Sandy beige, soft browns, muted greens
              sandyBeige: '#F5DEB3',
              softBrown: '#D2B48C',
              earthBrown: '#A0522D',
              mutedGreen: '#8FBC8F',
              forestGreen: '#6B8E23',

              // Accent Colors - Coral orange, pastel purples, soft reds, buttery yellows
              coralOrange: '#FF7F50',
              pastelPurple: '#DDA0DD',
              lavender: '#E6E6FA',
              softRed: '#F08080',
              butteryYellow: '#F0E68C',
              sunsetOrange: '#FFB347',

              // Neutral Tones - Creams, off-whites, parchment
              creamWhite: '#FFF8DC',
              offWhite: '#FAF0E6',
              parchment: '#F7E7CE',
              warmGray: '#F5F5DC',

              // Legacy colors for compatibility (updated to warmer tones)
              billiePurple: '#DDA0DD', // Now pastel purple
              billieGold: '#F0E68C',   // Now buttery yellow
              billieBlack: '#8B4513',  // Now warm brown
              billieRed: '#F08080',    // Now soft red
              billieBodyText: '#5D4037', // Warm brown text
              billieHeading: '#3E2723', // Dark brown headings
              billieAccent: '#FF7F50', // Coral orange accent
            },
            fontFamily: {
              sans: ['Nunito', 'Inter', 'sans-serif'],
              display: ['Fredoka One', 'sans-serif'],
              storybook: ['Kalam', 'cursive'],
              handwritten: ['Caveat', 'cursive'],
            },
            animation: {
              'gentle-glow': 'gentleGlow 3s ease-in-out infinite alternate',
              'soft-bounce': 'softBounce 2s ease-in-out infinite',
              'paper-flutter': 'paperFlutter 4s ease-in-out infinite',
              'sparkle': 'sparkle 2s ease-in-out infinite',
              'leaf-fall': 'leafFall 8s linear infinite',
              'warm-pulse': 'warmPulse 3s ease-in-out infinite alternate',
              'float': 'float 6s ease-in-out infinite',
              'storybook-wiggle': 'storybookWiggle 1s ease-in-out infinite',
              'spin-slow': 'spin 8s linear infinite',
              'bounce-slow': 'bounce 3s infinite',
              'pulse-slow': 'pulse 4s infinite',
            },
            keyframes: {
              gentleGlow: {
                '0%, 100%': {
                  boxShadow: '0 0 10px rgba(255, 127, 80, 0.3), 0 0 20px rgba(255, 127, 80, 0.2)',
                  filter: 'brightness(1)'
                },
                '50%': {
                  boxShadow: '0 0 20px rgba(255, 127, 80, 0.5), 0 0 30px rgba(255, 127, 80, 0.3)',
                  filter: 'brightness(1.1)'
                },
              },
              softBounce: {
                '0%, 100%': { transform: 'translateY(0px)' },
                '50%': { transform: 'translateY(-5px)' },
              },
              paperFlutter: {
                '0%, 100%': { transform: 'rotate(0deg) translateY(0px)' },
                '25%': { transform: 'rotate(1deg) translateY(-2px)' },
                '50%': { transform: 'rotate(0deg) translateY(-4px)' },
                '75%': { transform: 'rotate(-1deg) translateY(-2px)' },
              },
              sparkle: {
                '0%, 100%': { opacity: '0.3', transform: 'scale(0.8)' },
                '50%': { opacity: '1', transform: 'scale(1.2)' },
              },
              leafFall: {
                '0%': { transform: 'translateY(-100vh) rotate(0deg)' },
                '100%': { transform: 'translateY(100vh) rotate(360deg)' },
              },
              warmPulse: {
                '0%, 100%': {
                  textShadow: '0 0 5px rgba(240, 230, 140, 0.5)',
                  opacity: '1'
                },
                '50%': {
                  textShadow: '0 0 15px rgba(240, 230, 140, 0.8)',
                  opacity: '0.9'
                },
              },
              float: {
                '0%': { transform: 'translateY(0px)' },
                '50%': { transform: 'translateY(-10px)' },
                '100%': { transform: 'translateY(0px)' },
              },
              storybookWiggle: {
                '0%, 100%': { transform: 'rotate(0deg)' },
                '25%': { transform: 'rotate(1deg)' },
                '75%': { transform: 'rotate(-1deg)' },
              },
            },
            boxShadow: {
              'warm-glow': '0 0 15px rgba(255, 127, 80, 0.4), 0 0 25px rgba(255, 127, 80, 0.2)',
              'soft-shadow': '0 4px 20px rgba(139, 69, 19, 0.3)',
              'paper-shadow': '0 2px 10px rgba(139, 69, 19, 0.2)',
              'storybook': '0 8px 32px rgba(255, 127, 80, 0.3)',
              'glow-coral': '0 0 20px rgba(255, 127, 80, 0.5)',
              'glow-yellow': '0 0 20px rgba(240, 230, 140, 0.5)',
              'glow-purple': '0 0 20px rgba(221, 160, 221, 0.5)',
            },
            backgroundImage: {
              'storybook-gradient': 'linear-gradient(45deg, #87CEEB, #FF7F50, #F0E68C, #DDA0DD)',
              'paper-texture': 'radial-gradient(ellipse at center, rgba(247, 231, 206, 0.8) 0%, rgba(245, 245, 220, 0.6) 70%)',
              'sky-gradient': 'linear-gradient(to bottom, #87CEEB 0%, #5DADE2 50%, #4682B4 100%)',
              'warm-gradient': 'linear-gradient(135deg, #F5DEB3 0%, #D2B48C 50%, #A0522D 100%)',
            },
            backgroundSize: {
              'grid': '50px 50px',
            },
          }
        }
      }
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700;900&family=Fredoka+One:wght@400&family=Kalam:wght@400;700&family=Caveat:wght@400;700&display=swap" rel="stylesheet">
  <script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react-router-dom": "https://esm.sh/react-router-dom@^7.6.2",
    "react/": "https://esm.sh/react@^19.1.0/"
  }
}
</script>

</head>
  <body class="bg-creamWhite text-billieBodyText font-sans">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html>
    