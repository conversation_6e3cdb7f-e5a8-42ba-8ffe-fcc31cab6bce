
import React from 'react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  hoverEffect?: boolean;
}

const Card: React.FC<CardProps> = ({ children, className, onClick, hoverEffect = false }) => {
  const baseClasses = "bg-parchment/90 backdrop-blur-sm rounded-xl shadow-paper-shadow overflow-hidden border-2 border-softBrown/30 relative";
  const hoverClasses = hoverEffect ? "transition-all duration-300 ease-in-out hover:shadow-storybook hover:scale-[1.02] hover:border-coralOrange/50 animate-gentle-glow" : "";

  return (
    <div
      className={`${baseClasses} ${hoverClasses} ${className || ''}`}
      onClick={onClick}
      role={onClick ? "button" : undefined}
      tabIndex={onClick ? 0 : undefined}
      onKeyDown={onClick ? (e) => (e.key === 'Enter' || e.key === ' ') && onClick() : undefined}
    >
      {/* Paper texture background */}
      <div className="absolute inset-0 bg-paper-texture opacity-40 pointer-events-none" />
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
};

export default Card;
    